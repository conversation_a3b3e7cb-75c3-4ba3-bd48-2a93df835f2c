// invite.js
const router = require('../../utils/router');

Page({
    data: {
        statusBarHeight: 20,
        navbarHeight: 0,
        gameInfo: {},
        gameTypeName: '',
        invitedPlayers: [],
        joinedCount: 0,
        canStartGame: false,
        inviteId: '',
        previewTitle: ''
    },

    onLoad() {
        // 获取系统信息设置状态栏高度
        const systemInfo = wx.getSystemInfoSync();
        this.setData({
            statusBarHeight: systemInfo.statusBarHeight
        });

        // 获取游戏信息
        const gameInfo = wx.getStorageSync('currentGameInfo');
        if (!gameInfo) {
            wx.showToast({
                title: '游戏信息获取失败',
                icon: 'error'
            });
            setTimeout(() => {
                wx.navigateBack();
            }, 1500);
            return;
        }

        // 生成邀请ID
        const inviteId = this.generateInviteId();

        // 设置游戏类型名称
        const gameTypeName = this.getGameTypeName(gameInfo.type);

        this.setData({
            gameInfo,
            gameTypeName,
            inviteId,
            previewTitle: `邀请你加入「${gameInfo.name}」`
        });

        // 保存邀请信息到本地
        this.saveInviteInfo();
    },

    onShow() {
        // 每次显示页面时刷新状态
        this.refreshStatus();
    },

    onHide() {
        // 页面隐藏时记录状态
        wx.setStorageSync('invite_state_' + this.data.inviteId, {
            invitedPlayers: this.data.invitedPlayers,
            joinedCount: this.data.joinedCount,
            canStartGame: this.data.canStartGame
        });
    },

    // 自定义导航栏高度设置
    onNavbarHeight(e) {
        this.setData({
            navbarHeight: e.detail
        });
    },

    // 导航栏回调函数
    onNavBack() {
        this.navigateBack();
    },

    onNavHome() {
        router.switchTab('gameList');
    },

    navigateBack() {
        // 检查页面栈
        const pages = getCurrentPages();
        if (pages.length > 1) {
            router.navigateBack();
        } else {
            // 如果没有上一页，返回首页
            router.switchTab('gameList');
        }
    },

    generateInviteId() {
        // 生成唯一的邀请ID
        const timestamp = Date.now().toString(36);
        const random = Math.random().toString(36).substr(2, 5);
        return `inv_${timestamp}_${random}`;
    },

    getGameTypeName(type) {
        const typeMap = {
            'mahjong': '麻将',
            'poker': '扑克',
            'board': '棋类',
            'other': '其他'
        };
        return typeMap[type] || '未知';
    },

    saveInviteInfo() {
        // 获取用户信息
        const userInfo = wx.getStorageSync('userInfo') || {};

        // 创建邀请信息
        const inviteInfo = {
            id: this.data.inviteId,
            gameInfo: this.data.gameInfo,
            createTime: Date.now(),
            players: [{
                id: Date.now().toString(),
                name: userInfo.nickName || '创建者',
                avatar: userInfo.avatarUrl || '/static/images/default-avatar.png',
                status: 'joined',
                joinTime: Date.now()
            }],
            status: 'waiting' // waiting, started, ended
        };

        // 保存到本地存储（实际项目中应该保存到服务器）
        wx.setStorageSync(`invite_${this.data.inviteId}`, inviteInfo);

        // 更新页面数据
        this.setData({
            invitedPlayers: inviteInfo.players,
            joinedCount: 1
        });
    },

    shareToWechat() {
        // 微信分享逻辑在 onShareAppMessage 中处理
        console.log('分享到微信');
        // 保存当前状态，以便分享后恢复
        wx.setStorageSync('invite_state_' + this.data.inviteId, {
            invitedPlayers: this.data.invitedPlayers,
            joinedCount: this.data.joinedCount,
            canStartGame: this.data.canStartGame
        });
    },

    refreshStatus() {
        // 首先检查是否有保存的状态
        const savedState = wx.getStorageSync('invite_state_' + this.data.inviteId);
        if (savedState) {
            this.setData(savedState);
            // 使用后清除
            wx.removeStorageSync('invite_state_' + this.data.inviteId);
            return;
        }

        // 模拟刷新邀请状态
        const inviteInfo = wx.getStorageSync(`invite_${this.data.inviteId}`);
        if (inviteInfo && inviteInfo.players) {
            const joinedCount = inviteInfo.players.filter(p => p.status === 'joined').length;
            const canStartGame = joinedCount >= this.data.gameInfo.playerCount;

            this.setData({
                invitedPlayers: inviteInfo.players,
                joinedCount,
                canStartGame
            });
        }
    },

    startGame() {
        if (!this.data.canStartGame) {
            wx.showToast({
                title: '等待更多玩家加入',
                icon: 'error'
            });
            return;
        }

        // 创建游戏并跳转到记分页面
        const gameData = {
            ...this.data.gameInfo,
            id: Date.now().toString(),
            createTime: Date.now(),
            players: this.data.invitedPlayers.filter(player => player.status === 'joined').map((player, index) => ({
                id: player.id,
                name: player.name,
                avatar: player.avatar,
                score: this.data.gameInfo.initialScore,
                selected: false
            })),
            rounds: []
        };

        // 保存游戏数据
        const games = wx.getStorageSync('games') || [];
        games.unshift(gameData);
        wx.setStorageSync('games', games);

        // 跳转到记分页面
        router.redirectTo('score', {
            id: gameData.id
        });
    },

    // 分享配置
    onShareAppMessage() {
        return {
            title: `邀请你加入「${this.data.gameInfo.name}」`,
            path: `/pages/join/index?inviteId=${this.data.inviteId}`,
            imageUrl: '/static/images/logo-small.png'
        };
    }
});