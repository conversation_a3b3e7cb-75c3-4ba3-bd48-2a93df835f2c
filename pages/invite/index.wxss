/**invite.wxss**/
/* 覆盖全局样式，确保页面可以滚动 */
page {
  height: auto !important;
  background-color: #f8f4e9 !important; /* 与首页匹配的米色背景 */
}

.container {
  min-height: 100vh;
  background-color: #f8f4e9; /* 与首页匹配的米色背景 */
  /* 不使用flex布局，让内容自然流动 */
}

/* 背景麻将装饰元素 - 与设计标准保持一致 */
.bg-elements {
  position: fixed;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;
}

.mahjong-tile {
  position: absolute;
  width: 60rpx;
  height: 80rpx;
  background-color: #ffffff;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.08);
  transform: rotate(var(--rotation));
  opacity: 0.05; /* 降低透明度，减少视觉干扰 */
  z-index: -1;
}

.mahjong-1 {
  --rotation: 15deg;
  top: 15%;
  left: 8%;
}

.mahjong-2 {
  --rotation: -10deg;
  top: 25%;
  right: 12%;
}

.mahjong-3 {
  --rotation: 5deg;
  bottom: 30%;
  left: 5%;
}

.mahjong-4 {
  --rotation: -20deg;
  bottom: 20%;
  right: 8%;
}

/* 自定义导航栏右侧操作按钮 */
.nav-action {
  width: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-icon {
  font-size: 36rpx;
  font-weight: 600;
  color: #8B0000; /* 更改为深红色 */
}

/* 重新定义content，确保不与全局样式冲突 */
.score-scroll-view {
  background-color: transparent;
  box-sizing: border-box;
  overflow-x: hidden;
  width: 100%;
}

.content {
  padding: 32rpx;
  padding-bottom: 0; /* 移除底部内边距，由bottom-spacer处理 */
  margin-top: 0 !important; /* 覆盖全局样式的 margin-top: 88px */
  flex: none !important; /* 覆盖全局样式的 flex: 1 */
  min-height: auto;
  box-sizing: border-box;
  width: 100%;
  position: relative;
}

/* 标题样式 */
.mahjong-section-title {
  position: relative;
  display: inline-block;
  padding: 12rpx 20rpx; /* 与设计标准保持一致 */
  background: linear-gradient(135deg, #e6c46c, #d4af37);
  border-radius: 12rpx; /* 统一圆角 */
  color: #8B0000;
  font-weight: 600;
  box-shadow: 0 4rpx 16rpx rgba(212, 175, 55, 0.25); /* 统一阴影效果 */
  font-size: 28rpx; /* 统一字体大小 */
  margin-bottom: 16rpx;
}

/* 游戏信息卡片 */
.game-info-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.08), 0 2rpx 8rpx rgba(0,0,0,0.04);
  border-left: 8rpx solid #d4af37; /* 金色边框 */
}

.game-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.game-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #8B0000; /* 深红色 */
  margin-bottom: 12rpx;
}

.game-details {
  font-size: 28rpx;
  color: #666;
}

/* 邀请方式区域 */
.invite-section {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.08), 0 2rpx 8rpx rgba(0,0,0,0.04);
  border-left: 8rpx solid #d4af37; /* 金色边框 */
}

/* 邀请方式网格布局 */
.invite-methods-grid {
  display: flex;
  justify-content: center;
  gap: 16rpx;
}

.invite-method-card {
  width: 100%;
  max-width: 400rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32rpx 16rpx;
  background-color: rgba(248, 244, 233, 0.6); /* 米色背景 */
  border-radius: 16rpx;
  border: 2rpx solid rgba(212, 175, 55, 0.2); /* 金色边框 */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-align: center;
  min-height: 160rpx;
  justify-content: center;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.06);
  margin: 0 auto; /* 居中显示 */
}

/* 微信分享卡片特色背景 */
.share-card {
  background: linear-gradient(135deg, rgba(230, 196, 108, 0.2), rgba(248, 244, 233, 0.6));
}

.share-card:hover::before,
.share-card:active::before {
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.15), rgba(212, 175, 55, 0.08));
}

/* 通用hover效果 */
.invite-method-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.invite-method-card:hover::before,
.invite-method-card:active::before {
  opacity: 1;
}

.invite-method-card:hover,
.invite-method-card:active {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 24rpx rgba(212, 175, 55, 0.2);
  border-color: #d4af37;
}

.share-card {
  border: 2rpx solid rgba(212, 175, 55, 0.2); /* 金色边框 */
  background-color: rgba(248, 244, 233, 0.6); /* 米色背景 */
  font-size: inherit;
  line-height: inherit;
  padding: 32rpx 16rpx;
}

.share-card::after {
  border: none;
}

.card-icon {
  font-size: 64rpx;
  margin-bottom: 16rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0,0,0,0.1));
  position: relative;
  z-index: 1;
}

.card-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #8B0000; /* 深红色 */
  margin-bottom: 8rpx;
  position: relative;
  z-index: 1;
}

.card-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
  position: relative;
  z-index: 1;
}

/* 分享预览区域样式 */
.share-preview {
  margin-top: 32rpx;
  padding-top: 24rpx;
  border-top: 1rpx dashed rgba(212, 175, 55, 0.3);
}

.preview-title {
  font-size: 26rpx;
  color: #8B0000;
  margin-bottom: 16rpx;
  text-align: center;
  font-weight: 500;
}

.preview-card {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.06);
  border: 1rpx solid rgba(212, 175, 55, 0.2);
}

.preview-content {
  flex: 1;
}

.preview-game-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.preview-desc {
  font-size: 24rpx;
  color: #666;
}

.preview-logo {
  width: 60rpx;  /* 控制logo大小，改为更小 */
  height: 60rpx; /* 控制logo大小，改为更小 */
  border-radius: 8rpx;
  margin-left: 16rpx;
  flex-shrink: 0;
}

/* 响应式优化 */
@media screen and (max-width: 600rpx) {
  .invite-methods-grid {
    flex-direction: column;
    gap: 12rpx;
  }
  
  .invite-method-card {
    flex-direction: row;
    min-height: auto;
    padding: 24rpx;
    text-align: left;
  }
  
  .card-icon {
    font-size: 48rpx;
    margin-bottom: 0;
    margin-right: 24rpx;
    flex-shrink: 0;
  }
  
  .card-content {
    flex: 1;
  }
  
  .card-title {
    margin-bottom: 4rpx;
  }
}

/* 为移动端模式添加内容包装 */
.invite-method-card .card-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

@media screen and (max-width: 600rpx) {
  .invite-method-card .card-content {
    align-items: flex-start;
    text-align: left;
  }
}



/* 已邀请玩家列表 */
.invited-players {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.08), 0 2rpx 8rpx rgba(0,0,0,0.04);
  border-left: 8rpx solid #d4af37; /* 金色边框 */
}

.player-list {
  margin-top: 24rpx;
}

.player-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid rgba(212, 175, 55, 0.2); /* 金色边框 */
}

.player-item:last-child {
  border-bottom: none;
}

.player-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 16rpx; /* 更圆润的边角 */
  margin-right: 24rpx;
  border: 2rpx solid rgba(212, 175, 55, 0.3); /* 金色边框 */
}

.player-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.player-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.player-status {
  font-size: 24rpx;
  color: #8B0000; /* 深红色 */
}

.player-status-icon {
  font-size: 32rpx;
  width: 40rpx;
  text-align: center;
}

.player-status-icon.joined {
  color: #52c41a;
}

.player-status-icon.invited {
  color: #d4af37; /* 金色 */
}

/* 底部操作按钮 */
.action-buttons {
  display: flex;
  gap: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
}

.btn {
  flex: 1;
  height: 70rpx; /* 与参考标准保持一致 */
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16rpx; /* 统一圆角，与参考标准保持一致 */
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4rpx 16rpx rgba(212, 175, 55, 0.25); /* 统一阴影效果，与参考标准保持一致 */
  letter-spacing: 4rpx;
  position: relative;
  overflow: hidden;
  padding: 24rpx; /* 统一内边距，与参考标准保持一致 */
}

.btn:active {
  transform: scale(0.98); /* 统一点击效果，与参考标准保持一致 */
}

.btn-secondary {
  background-color: rgba(248, 244, 233, 0.8); /* 米色背景 */
  color: #8B0000; /* 深红色 */
  border: 1rpx solid #d4af37; /* 金色边框 */
}

.btn-secondary:active {
  background-color: rgba(230, 196, 108, 0.25);
}

.btn-primary {
  background: linear-gradient(135deg, #e6c46c, #d4af37); /* 金色渐变 */
  color: #8B0000; /* 深红色 */
}

.btn-primary:active {
  background-color: rgba(230, 196, 108, 0.25);
}

.btn[disabled] {
  background: #cccccc !important;
  color: #ffffff !important;
  opacity: 0.6;
}

button[disabled] {
  background-color: #cccccc !important;
  color: #ffffff !important;
  opacity: 0.6;
}

/* 底部间距 */
.bottom-spacer {
  height: 120rpx; /* 增加底部间距，确保内容不被底部导航栏遮挡 */
}

/* 底部固定导航栏 */
.bottom-nav-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: calc(100rpx + env(safe-area-inset-bottom)); /* 调整高度，与createGame页面保持一致 */
  background-color: #ffffff;
  border-top: 1rpx solid #eeeeee;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 32rpx;
  box-sizing: border-box;
  z-index: 100;
  box-shadow: 0 -1rpx 6rpx rgba(0, 0, 0, 0.05);
}

.bottom-nav-bar .action-buttons {
  width: 100%;
  margin-bottom: 0;
  padding: 0;
  display: flex;
  gap: 24rpx;
}

.bottom-nav-bar .btn {
  height: 70rpx; /* 与参考标准保持一致 */
  padding: 0 24rpx; /* 调整内边距 */
}