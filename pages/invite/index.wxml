<!--invite.wxml-->
<view class="container">
  <!-- 背景麻将装饰元素 -->
  <view class="bg-elements">
    <view class="mahjong-tile mahjong-1"></view>
    <view class="mahjong-tile mahjong-2"></view>
    <view class="mahjong-tile mahjong-3"></view>
    <view class="mahjong-tile mahjong-4"></view>
  </view>

  <!-- 自定义导航栏 -->
  <custom-navbar
    title="邀请好友"
    show-back="{{true}}"
    show-home="{{true}}"
    bind:back="onNavBack"
    bind:home="onNavHome"
  ></custom-navbar>

  <!-- 可滚动内容区域 -->
  <scroll-view class="scroll-content" scroll-y="true" style="height: calc(100vh - {{navbarHeight}}px - 100rpx); margin-top: {{navbarHeight}}px;">
    <view class="content">
      <!-- 游戏信息展示 -->
      <view class="game-info-card">
        <view class="game-header">
          <view class="game-name">{{gameInfo.name}}</view>
          <view class="game-details">{{gameInfo.playerCount}}人 · {{gameTypeName}}</view>
        </view>
      </view>

      <!-- 邀请方式 -->
      <view class="invite-section">
        <view class="mahjong-section-title">邀请方式</view>
        
        <view class="invite-methods-grid">
          <!-- 微信分享 -->
          <button class="invite-method-card share-card" bindtap="shareToWechat" open-type="share">
            <view class="card-icon">💬</view>
            <view class="card-content">
              <view class="card-title">微信分享</view>
              <view class="card-desc">分享到群聊</view>
            </view>
          </button>
        </view>
        
        <!-- 分享预览区域 -->
        <view class="share-preview">
          <view class="preview-title">分享预览</view>
          <view class="preview-card">
            <view class="preview-content">
              <view class="preview-game-name">{{gameInfo.name}}</view>
              <view class="preview-desc">{{previewTitle}}</view>
            </view>
            <image class="preview-logo" src="/static/images/logo.png" mode="aspectFit"></image>
          </view>
        </view>
      </view>

      <!-- 已邀请玩家列表 -->
      <view class="invited-players" wx:if="{{invitedPlayers.length > 0}}">
        <view class="mahjong-section-title">已邀请玩家 ({{invitedPlayers.length}}/{{gameInfo.playerCount}})</view>
        <view class="player-list">
          <view class="player-item" wx:for="{{invitedPlayers}}" wx:key="id">
            <image class="player-avatar" src="{{item.avatar || '/static/images/default-avatar.png'}}" mode="aspectFill"></image>
            <view class="player-info">
              <text class="player-name">{{item.name}}</text>
              <text class="player-status">{{item.status === 'joined' ? '已加入' : '邀请中'}}</text>
            </view>
            <view class="player-status-icon {{item.status}}">
              {{item.status === 'joined' ? '✓' : '⏳'}}
            </view>
          </view>
        </view>
      </view>

      <!-- 移除原来的action-buttons，为底部留出空间 -->
      <view class="bottom-spacer"></view>
    </view>
  </scroll-view>

  <!-- 底部固定导航栏 -->
  <view class="bottom-nav-bar">
    <view class="action-buttons">
      <button class="btn btn-secondary" bindtap="refreshStatus">刷新状态</button>
      <button
        class="btn btn-primary"
        bindtap="startGame"
        disabled="{{!canStartGame}}"
      >
        开始游戏 ({{joinedCount}}/{{gameInfo.playerCount}})
      </button>
    </view>
  </view>
</view>
